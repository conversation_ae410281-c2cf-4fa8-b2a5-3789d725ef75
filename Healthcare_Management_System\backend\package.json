{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "author": "", "license": "ISC", "dependencies": {"bcrypt": "^5.1.1", "cloudinary": "^2.0.2", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.18.3", "express-fileupload": "^1.4.3", "express-session": "^1.18.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.11.0", "mongoose": "^8.2.1", "validator": "^13.11.0"}}