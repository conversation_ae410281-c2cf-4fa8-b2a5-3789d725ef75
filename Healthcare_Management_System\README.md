# Healthcare_Management_System

Healthcare Management System using MongoDb, Express, node and React

Need to work on
Ability to accept the appointment by the doctor to acknowledge the patient that their appointment has been approved.

User should not be allowed to register if he/she tries to provide the already registered email ID. The password should be encrypted and the password field shouldn't be displayed in the admin panel.

Allows doctors and healthcare providers to create and manage their profiles, including specialties, availability, and contact information.

Language and Technology used:

Html,css

Javascript

React

MongoDb

Express

Node

Postman API


Installing - easy ::

Download the repository

git clone https://github.com/avnishsharma1209/Healthcare_Management_System

Open the Terminal PowerShell (Windows) and change directory to the project folder.

Go to frontend folder and type " npm install " in the terminal and press Enter.All the dependencies of frontend would be installed.

cd frontend 

npm install 

Go to backed folder and type " npm install " in the terminal and press Enter.All the dependencies of backed would be installed.

cd backend  

Go back to the Terminal (PowerShell) and be sure that you are pointing inside the project folder. To open the application, type ‘node server.js ’ and press Enter.


The server should be live on the local port 4002.


Type http://localhost:5173/ into a browser.


Now you should be inside the application


Screenshots


![WhatsApp Image 2024-11-29 at 08 59 50_0308802c](https://github.com/user-attachments/assets/becad677-1efa-4c3e-b1f0-dac808bb2b64)

![WhatsApp Image 2024-11-29 at 08 59 31_992f4c7b](https://github.com/user-attachments/assets/90c95c5c-b286-4545-9f78-29d1abe82121)

![WhatsApp Image 2024-11-29 at 08 59 32_0a948498](https://github.com/user-attachments/assets/b5a4e58a-ecaa-406f-95c8-9608950d4f4d)

![WhatsApp Image 2024-11-29 at 08 59 32_31bd41a8](https://github.com/user-attachments/assets/b8cfea28-e4ff-40e3-96b1-58fdd3a9b922)

![WhatsApp Image 2024-11-29 at 09 00 04_0dfdd252](https://github.com/user-attachments/assets/5400cb49-0908-4523-88fd-8477ad5216ba)


Data Store in Mongodb

 ![WhatsApp Image 2024-11-29 at 09 07 16_969c5717](https://github.com/user-attachments/assets/d198ca2d-1804-4cb3-b4d7-d429544a6654)






