@import url("https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap");
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  overflow-x: hidden;
  font-family: "Montserrat", sans-serif;
}
body {
  background: #e5e5e5;
}
.btn {
  padding: 7px 20px;
  border-radius: 12px;
  border: none;
  font-weight: bold;
  letter-spacing: 2px;
}
.white-btn {
  background: #fff;
  color: #111;
}
.purple-btn {
  background: #9083d5;
  color: #fff;
}
p,
span,
a {
  font-size: 20px;
}
h1 {
  font-size: 36px;
  font-weight: 900;
}
h1 {
  font-size: 36px;
  font-weight: 900;
}
h2 {
  font-size: 32px;
  font-weight: 900;
}
h3 {
  font-size: 28px;
  font-weight: 900;
}
h4 {
  font-size: 24px;
  font-weight: 900;
}
h5,
h6 {
  font-size: 24px;
  font-weight: 700;
}
@media (max-width: 1186px) {
  h1 {
    font-size: 32px;
    font-weight: 900;
  }
  h2 {
    font-size: 30px;
    font-weight: 900;
  }
}
.container {
  padding: 0 100px;
}
@media (max-width: 700px) {
  .container {
    padding: 0 20px;
  }
}
.hero {
  display: flex;
  gap: 50px;
  padding-top: 120px;
  padding-bottom: 120px;
  position: relative;
}
.hero .banner {
  flex: 1;
}
.hero .banner h1,
.hero .banner p {
  max-width: 650px;
}
.hero .banner h1 {
  letter-spacing: 2px;
  word-spacing: 5px;
  font-size: 40px;
}
.hero .banner p {
  color: #111;
  letter-spacing: 2px;
}
.hero .banner span {
  position: absolute;
  right: -300px;
  top: -200px;
  z-index: -1;
}
.hero .banner:first-child {
  display: flex;
  justify-content: center;
  flex-direction: column;
  gap: 50px;
}
.hero .banner:last-child {
  display: flex;
  justify-content: center;
  align-items: center;
  overflow-y: hidden;
}
.animated-image {
  animation: moveUpDown 1s infinite alternate ease-in-out;
}

@keyframes moveUpDown {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(20px);
  }
}
@media (max-width: 1186px) {
  .hero .banner span {
    right: -400px;
  }
  .hero .banner h1 {
    font-size: 32px;
  }
}
@media (max-width: 1085px) {
  .hero .banner span {
    right: -480px;
    top: -315px;
  }
}
@media (max-width: 925px) {
  .hero {
    flex-direction: column;
  }
  @media (max-width: 700px) {
    .hero {
      padding-bottom: 40px;
    }
  }
}
.form-component {
  padding-top: 40px;
  padding-bottom: 60px;
}
.form-component h2 {
  color: gray;
  letter-spacing: 4px;
  margin-bottom: 30px;
}
.form-component h4 {
  color: gray;
  font-weight: 700;
  margin-bottom: 20px;
}
.form-component p {
  max-width: 750px;
  color: gray;
  margin-bottom: 20px;
}
.form-component form {
  display: flex;
  flex-direction: column;
  gap: 30px;
}
.form-component form div {
  display: flex;
  gap: 30px;
}
.form-component form input,
.form-component form select,
.form-component form textarea {
  flex: 1;
  font-size: 24px;
  padding: 10px 10px 10px 40px;
  border-radius: 7px;
  border: 1px solid gray;
}
.form-component button {
  padding: 10px 35px;
  color: #fff;
  font-weight: 700;
  width: fit-content;
  border: none;
  border-radius: 8px;
  font-size: 24px;
  margin-bottom: 30px;
  background: linear-gradient(140deg, #9083d5, #271776ca);
}
.form-component .wrapper {
  display: flex;
  gap: 50px;
}
.form-component .wrapper .banner {
  flex: 1;
}
.form-component .wrapper .banner:last-child {
  display: flex;
  justify-content: center;
  align-items: center;
}
.form-component .wrapper .banner:last-child img {
  max-width: 450px;
}
.login-form {
  margin: 100px auto 20px auto;
  max-width: 800px;
  text-align: center;
}
.register-form {
  max-width: 1200px;
  margin: 100px auto 20px auto;
}
.login-form h2 {
  color: #000;
}
.register-form h2 {
  color: #000;
}
@media (max-width: 1110px) {
  .appointment-form form div:nth-child(4) {
    flex-direction: column;
  }
  .appointment-form form div:nth-child(5) {
    flex-direction: column;
  }
}
@media (max-width: 888px) {
  .form-component {
    padding-top: 30px;
    padding-bottom: 30px;
  }
  .form-component form div {
    flex-direction: column;
  }
}
@media (max-width: 667px) {
  .form-component form input,
  .form-component form select,
  .form-component form textarea {
    font-size: 20px;
    padding: 10px;
  }
}
.biography {
  display: flex;
  gap: 50px;
  padding-top: 40px;
  padding-bottom: 60px;
}
.biography .banner:first-child {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}
.biography .banner:last-child {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.biography .banner h3 {
  font-weight: 700;
  letter-spacing: 3px;
}
.biography .banner p:first-child {
  font-size: 24px;
  letter-spacing: 2px;
}
@media (max-width: 925px) {
  .biography {
    flex-direction: column-reverse;
  }
}
.message-form {
  position: relative;
}
.message-form h2 {
  text-align: center;
  color: #000;
  position: relative;
}
.message-form img {
  position: absolute;
  top: 0;
  right: -16%;
  height: 600px;
  z-index: -1;
}
@media (max-width: 700px) {
  .message-form img {
    right: -35%;
    height: 450px;
    top: 10%;
  }
}
footer {
  padding-bottom: 30px !important;
}
footer hr {
  margin-bottom: 30px;
}
footer .content {
  display: flex;
  gap: 20px;
}
footer .content div {
  flex: 1;
}
footer .content div:nth-child(3) {
  flex: 2;
}
footer .content div h4 {
  font-weight: 700;
  margin-bottom: 20px;
}
footer .content div ul {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
footer .content div ul a {
  color: gray;
  text-decoration: none;
}
footer .content div ul a:hover {
  color: #8570ed;
  transition: 0.3s;
}
footer .content div ul li span {
  color: gray;
}
footer .content div ul li span:first-child {
  width: 150px;
  display: inline-block;
}

footer .content div:last-child div {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 10px;
}
footer .content div:last-child div svg {
  font-size: 24px;
}
@media (max-width: 1135px) {
  footer .content {
    flex-wrap: wrap;
    justify-content: space-between;
  }
  footer .content div,
  footer .content div:nth-child(3) {
    flex: none;
    width: 340px;
    margin-bottom: 30px;
  }
}
@media (max-width: 900px) {
  footer {
    padding-bottom: 0 !important;
  }
  footer .content div,
  footer .content div:nth-child(3) {
    width: 100%;
  }
}
@media (max-width: 390px) {
  footer .content div:nth-child(3) ul li {
    display: flex;
    flex-direction: column;
  }
}

.departments {
  padding-top: 30px;
  padding-bottom: 50px;
}
.departments h2 {
  color: gray;
  margin-bottom: 30px;
}
.card {
  position: relative;
  border-radius: 8px;
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  padding-bottom: 15px;
  padding-left: 10px;
  padding-right: 20px;
  min-height: 360px;
  margin: 0 10px;
  text-decoration: none;
}
.card img {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: -1;
  top: 0;
  left: 0;
}
.card .depart-name {
  margin-bottom: 30px;
  background: #e5e5e5;
  width: 320px;
  font-size: 24px;
  text-transform: uppercase;
  display: flex;
  justify-content: center;
  padding: 12px;
  border-radius: 30px;
  left: 0;
  height: fit-content;
  font-weight: 700;
}

/**********************************************************************************************************************/
nav {
  display: flex;
  width: 100%;
  margin: 0 auto;
  justify-content: space-between;
  position: absolute;
  align-items: center;
  border-bottom-right-radius: 7px;
  border-bottom-left-radius: 7px;
  padding-top: 20px !important;
  padding-bottom: 20px !important;
  z-index: 2;
}
nav .logo {
  flex: 1;
  display: flex;
  align-items: center;
  font-size: 2rem;
}
nav .navLinks {
  flex: 2;
}
nav .navLinks {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
nav .navLinks .links {
  display: flex;
  gap: 25px;
}
nav .navLinks .links a {
  text-decoration: none;
  color: #222;
  font-size: 20px;
  font-weight: 600;
  letter-spacing: 1.4px;
}
nav .navLinks .links a:hover {
  color: #000;
  transition: 0.3s;
  cursor: pointer;
}
nav .btn {
  padding: 8px 20px;
  color: #e5e5e5;
  background: transparent;
  border: none;
  border-radius: 20px;
  font-size: 20px;
  font-weight: 600;
  z-index: 2;
}
nav .logoutBtn {
  background: linear-gradient(135deg, black, rgb(49, 49, 49));
}
nav .loginBtn {
  background: linear-gradient(135deg, black, rgb(49, 49, 49));
}
nav .hamburger {
  display: none;
}
nav svg {
  font-size: 1.75rem;
}
@media (max-width: 1520px) {
  nav {
    min-width: 100%;
    padding: 25px 20px 40px 20px;
  }
}
@media (max-width: 1100px) {
  nav {
    background: #ffffffbf;
    position: fixed;
    top: 0;
    left: 0;
  }
  nav .hamburger {
    display: flex;
  }
  nav .navLinks {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    padding: 15px 7.5px;
    z-index: 2;
    transition: 0.3s;
    position: fixed;
    left: 0;
    height: 100%;
    width: 400px;
    background: #ffffffbf;
    top: 79px;
  }
  nav .showmenu {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    padding: 15px 7.5px;
    z-index: 2;
    transition: 0.3s;
    position: fixed;
    left: -100%;
    width: 400px;
    height: 100%;
    background: #ffffffbf;
    top: 79px;
  }
  nav .navLinks .links {
    flex-direction: column;
    text-align: center;
  }
  nav .navLinks .links a {
    font-weight: 700;
  }
  nav .logoutBtn {
    margin-top: 20px;
  }
}
@media (max-width: 800px) {
  nav .showmenu {
    width: 96vw;
  }
}
@media (max-width: 650px) {
  nav .showmenu {
    margin: 0;
    width: 100%;
    border-radius: 0;
  }
}
@media (max-width: 555px) {
  nav {
    background: #fff;
  }
  nav .navLinks {
    background: #fff;
    width: 100%;
  }
}

input[type="date"]:before {
  content: attr(placeholder);
  color: #444;
  margin-right: 0.5em;
}
input[type="time"]:before {
  content: attr(placeholder);
  color: #444;
  margin-right: 0.5em;
}
.logo-img{
  width: 150px;
}
footer .logo-img{
  width: 100%;
}